-- ObjectiveTrackerHeaderTemplate.lua
-- 将XML模板转换为Lua代码实现

ObjectiveTrackerHeaderTemplate = {}

-- 创建目标追踪器头部模板框架
-- @param parent Frame 父框架
-- @param name string 框架名称
-- @return Frame 创建的框架对象
function ObjectiveTrackerHeaderTemplate:CreateFrame(parent, name)
    -- 创建主框架 - 对应XML中的Frame
    local frame = CreateFrame("Frame", name, parent)
    frame:SetSize(235, 25) -- 对应XML中的Size x="235" y="25"
    frame:Hide() -- 对应XML中的hidden="true"
    
    -- 创建ARTWORK层的纹理和字体元素
    ObjectiveTrackerHeaderTemplate:CreateArtworkLayer(frame)
    
    -- 创建动画组
    ObjectiveTrackerHeaderTemplate:CreateAnimations(frame)
    
    -- 设置脚本事件
    ObjectiveTrackerHeaderTemplate:SetupScripts(frame)
    
    return frame
end

-- 创建ARTWORK层的所有元素
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:CreateArtworkLayer(frame)
    -- Background纹理 - 对应XML中的Texture parentKey="Background"
    local background = frame:CreateTexture(nil, "ARTWORK")
    background:SetAtlas("Objective-Header", true) -- 对应atlas="Objective-Header" useAtlasSize="true"
    background:SetPoint("TOPLEFT", frame, "TOPLEFT", -29, 14) -- 对应Anchors中的设置
    background:SetAlpha(1) -- 对应alpha="1"
    background:Show() -- 对应hidden="false"
    frame.Background = background -- 对应parentKey="Background"
    
    -- Text字体字符串 - 对应XML中的FontString parentKey="Text"
    local text = frame:CreateFontString(nil, "ARTWORK", "GameFontNormalMed2")
    text:SetSize(170, 16) -- 对应Size x="170" y="16"
    text:SetPoint("LEFT", frame, "LEFT", 4, -1) -- 对应Anchors中的设置
    text:SetJustifyH("LEFT") -- 对应justifyH="LEFT"
    frame.Text = text -- 对应parentKey="Text"
    
    -- LineGlow纹理 - 对应XML中的Texture parentKey="LineGlow"
    local lineGlow = frame:CreateTexture(nil, "ARTWORK")
    lineGlow:SetAtlas("OBJFX_LineGlow", true) -- 对应atlas="OBJFX_LineGlow" useAtlasSize="true"
    lineGlow:SetPoint("LEFT", background, "LEFT", -50, 18) -- 对应relativeKey="$parent.Background"
    lineGlow:SetAlpha(0) -- 对应alpha="0"
    lineGlow:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    lineGlow:Show() -- 对应hidden="false"
    frame.LineGlow = lineGlow -- 对应parentKey="LineGlow"
    
    -- SoftGlow纹理 - 对应XML中的Texture parentKey="SoftGlow"
    local softGlow = frame:CreateTexture(nil, "ARTWORK")
    softGlow:SetAtlas("OBJFX_Glow", true) -- 对应atlas="OBJFX_Glow" useAtlasSize="true"
    softGlow:SetPoint("CENTER", background, "LEFT", 20, 20) -- 对应relativePoint="LEFT"
    softGlow:SetAlpha(0) -- 对应alpha="0"
    softGlow:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    softGlow:Show() -- 对应hidden="false"
    frame.SoftGlow = softGlow -- 对应parentKey="SoftGlow"
    
    -- StarBurst纹理 - 对应XML中的Texture parentKey="StarBurst"
    local starBurst = frame:CreateTexture(nil, "ARTWORK")
    starBurst:SetAtlas("OBJFX_StarBurst", true) -- 对应atlas="OBJFX_StarBurst" useAtlasSize="true"
    starBurst:SetPoint("CENTER", softGlow, "CENTER") -- 对应relativeKey="$parent.SoftGlow"
    starBurst:SetAlpha(0) -- 对应alpha="0"
    starBurst:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    starBurst:Show() -- 对应hidden="false"
    frame.StarBurst = starBurst -- 对应parentKey="StarBurst"
    
    -- LineSheen纹理 - 对应XML中的Texture parentKey="LineSheen"
    local lineSheen = frame:CreateTexture(nil, "ARTWORK")
    lineSheen:SetAtlas("OBJFX_LineBurst") -- 对应atlas="OBJFX_LineBurst"，注意没有useAtlasSize
    lineSheen:SetSize(60, 15) -- 对应Size x="60" y="15"
    lineSheen:SetPoint("CENTER", softGlow, "CENTER", 0, -13) -- 对应相对定位
    lineSheen:SetAlpha(0) -- 对应alpha="0"
    lineSheen:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    lineSheen:Show() -- 对应hidden="false"
    frame.LineSheen = lineSheen -- 对应parentKey="LineSheen"
end

-- 创建动画组 - 对应XML中的AnimationGroup
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:CreateAnimations(frame)
    -- 为Background创建动画组
    frame.BackgroundAnim = frame.Background:CreateAnimationGroup()

    -- Background的Alpha动画1 - 对应第一个Alpha childKey="Background"
    local bgAlpha1 = frame.BackgroundAnim:CreateAnimation("Alpha")
    bgAlpha1:SetStartDelay(0) -- 对应startDelay="0"
    bgAlpha1:SetDuration(0) -- 对应duration="0"
    bgAlpha1:SetChange(0) -- 对应fromAlpha="0" toAlpha="0"
    bgAlpha1:SetOrder(1) -- 对应order="1"

    -- Background的Alpha动画2 - 对应第二个Alpha childKey="Background"
    local bgAlpha2 = frame.BackgroundAnim:CreateAnimation("Alpha")
    bgAlpha2:SetStartDelay(0.25) -- 对应startDelay="0.25"
    bgAlpha2:SetDuration(0.5) -- 对应duration="0.5"
    bgAlpha2:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    bgAlpha2:SetOrder(1)

    -- 为LineGlow创建动画组
    frame.LineGlowAnim = frame.LineGlow:CreateAnimationGroup()

    -- LineGlow的Alpha动画1
    local lineGlowAlpha1 = frame.LineGlowAnim:CreateAnimation("Alpha")
    lineGlowAlpha1:SetDuration(0.15) -- 对应duration="0.15"
    lineGlowAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    lineGlowAlpha1:SetOrder(1)

    -- LineGlow的Alpha动画2
    local lineGlowAlpha2 = frame.LineGlowAnim:CreateAnimation("Alpha")
    lineGlowAlpha2:SetStartDelay(0.25)
    lineGlowAlpha2:SetDuration(0.65) -- 对应duration="0.65"
    lineGlowAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    lineGlowAlpha2:SetOrder(1)

    -- LineGlow的Scale动画
    local lineGlowScale = frame.LineGlowAnim:CreateAnimation("Scale")
    lineGlowScale:SetDuration(0.15)
    lineGlowScale:SetScale(2) -- 对应toScaleX="2"，3.3.5只支持单参数
    lineGlowScale:SetOrder(1)
    lineGlowScale:SetOrigin("CENTER", -50, 0) -- 对应Origin point="CENTER" Offset x="-50" y="0"

    -- LineGlow的Translation动画
    local lineGlowTranslation = frame.LineGlowAnim:CreateAnimation("Translation")
    lineGlowTranslation:SetDuration(0.75) -- 对应duration="0.75"
    lineGlowTranslation:SetOrder(1)
    lineGlowTranslation:SetOffset(50, 0) -- 对应offsetX="50" offsetY="0"

    -- 为SoftGlow创建动画组
    frame.SoftGlowAnim = frame.SoftGlow:CreateAnimationGroup()

    -- SoftGlow的Alpha动画1
    local softGlowAlpha1 = frame.SoftGlowAnim:CreateAnimation("Alpha")
    softGlowAlpha1:SetDuration(0.25)
    softGlowAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    softGlowAlpha1:SetOrder(1)

    -- SoftGlow的Alpha动画2
    local softGlowAlpha2 = frame.SoftGlowAnim:CreateAnimation("Alpha")
    softGlowAlpha2:SetStartDelay(0.25)
    softGlowAlpha2:SetDuration(0.5)
    softGlowAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    softGlowAlpha2:SetOrder(1)

    -- SoftGlow的Scale动画
    local softGlowScale = frame.SoftGlowAnim:CreateAnimation("Scale")
    softGlowScale:SetDuration(0.25)
    softGlowScale:SetScale(0.8) -- 对应toScaleX="0.8"，3.3.5只支持单参数
    softGlowScale:SetOrder(1)

    -- 为StarBurst创建动画组
    frame.StarBurstAnim = frame.StarBurst:CreateAnimationGroup()

    -- StarBurst的Alpha动画1
    local starBurstAlpha1 = frame.StarBurstAnim:CreateAnimation("Alpha")
    starBurstAlpha1:SetDuration(0.25)
    starBurstAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    starBurstAlpha1:SetOrder(1)

    -- StarBurst的Alpha动画2
    local starBurstAlpha2 = frame.StarBurstAnim:CreateAnimation("Alpha")
    starBurstAlpha2:SetStartDelay(0.25)
    starBurstAlpha2:SetDuration(0.5)
    starBurstAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    starBurstAlpha2:SetOrder(1)

    -- StarBurst的Scale动画
    local starBurstScale = frame.StarBurstAnim:CreateAnimation("Scale")
    starBurstScale:SetDuration(0.25)
    starBurstScale:SetScale(1) -- 对应toScaleX="1"，3.3.5只支持单参数
    starBurstScale:SetOrder(1)

    -- 为LineSheen创建动画组
    frame.LineSheenAnim = frame.LineSheen:CreateAnimationGroup()

    -- LineSheen的Alpha动画1
    local lineSheenAlpha1 = frame.LineSheenAnim:CreateAnimation("Alpha")
    lineSheenAlpha1:SetStartDelay(0.15) -- 对应startDelay="0.15"
    lineSheenAlpha1:SetDuration(0.5)
    lineSheenAlpha1:SetChange(0.75) -- 对应fromAlpha="0" toAlpha="0.75"
    lineSheenAlpha1:SetOrder(1)

    -- LineSheen的Alpha动画2
    local lineSheenAlpha2 = frame.LineSheenAnim:CreateAnimation("Alpha")
    lineSheenAlpha2:SetStartDelay(0.75) -- 对应startDelay="0.75"
    lineSheenAlpha2:SetDuration(0.5)
    lineSheenAlpha2:SetChange(-0.75) -- 对应fromAlpha="0.75" toAlpha="0"
    lineSheenAlpha2:SetOrder(1)

    -- LineSheen的Translation动画
    local lineSheenTranslation = frame.LineSheenAnim:CreateAnimation("Translation")
    lineSheenTranslation:SetStartDelay(0.15)
    lineSheenTranslation:SetDuration(1.5) -- 对应duration="1.5"
    lineSheenTranslation:SetOrder(1)
    lineSheenTranslation:SetOffset(250, 0) -- 对应offsetX="250" offsetY="0"

    -- 创建HeaderOpenAnim动画组来统一控制所有动画 - 对应XML中的AnimationGroup parentKey="HeaderOpenAnim"
    frame.HeaderOpenAnim = frame:CreateAnimationGroup()
    frame.HeaderOpenAnim:SetToFinalAlpha(true) -- 对应setToFinalAlpha="true"

    -- 设置动画完成事件 - 对应XML中的OnFinished function="ObjectiveTrackerHeader_OnAnimFinished"
    frame.HeaderOpenAnim:SetScript("OnFinished", function(self)
        ObjectiveTrackerHeader_OnAnimFinished(self)
    end)
end

-- 设置脚本事件 - 对应XML中的Scripts
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:SetupScripts(frame)
    -- OnLoad事件 - 对应XML中的OnLoad脚本
    frame:SetScript("OnLoad", function(self)
        self.height = 25 -- 对应XML中的脚本内容
    end)
end

-- 动画完成回调函数 - 对应XML中引用的函数
-- @param self AnimationGroup 动画组对象
function ObjectiveTrackerHeader_OnAnimFinished(self)
    -- 这里可以添加动画完成后的处理逻辑
    -- 具体实现需要根据实际需求来定义
    print("ObjectiveTrackerHeader animation finished")
end

-- 创建模板实例的便捷函数
-- @param parent Frame 父框架
-- @param name string 框架名称（可选）
-- @return Frame 创建的框架对象
function CreateObjectiveTrackerHeaderTemplate(parent, name)
    local frameName = name or ("ObjectiveTrackerHeader" .. math.random(1000, 9999))
    return ObjectiveTrackerHeaderTemplate:CreateFrame(parent, frameName)
end

-- 全局测试框架变量
local testFrame = nil

-- 斜杠命令：创建并显示测试框架
SLASH_OBJHEADERTEST1 = "/objheader"
SLASH_OBJHEADERTEST2 = "/testheader"
SlashCmdList["OBJHEADERTEST"] = function(msg)
    local cmd = string.lower(msg or "")

    if cmd == "show" or cmd == "" then
        -- 创建或显示测试框架
        if not testFrame then
            testFrame = CreateObjectiveTrackerHeaderTemplate(UIParent, "TestObjectiveTrackerHeader")
            testFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
            testFrame.Text:SetText("测试目标追踪器头部")
            print("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已创建")
        end
        testFrame:Show()
        print("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已显示")

    elseif cmd == "hide" then
        -- 隐藏测试框架
        if testFrame then
            testFrame:Hide()
            print("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已隐藏")
        else
            print("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
        end

    elseif cmd == "anim" or cmd == "play" then
        -- 播放动画
        if testFrame then
            -- 播放所有动画组
            if testFrame.BackgroundAnim then testFrame.BackgroundAnim:Play() end
            if testFrame.LineGlowAnim then testFrame.LineGlowAnim:Play() end
            if testFrame.SoftGlowAnim then testFrame.SoftGlowAnim:Play() end
            if testFrame.StarBurstAnim then testFrame.StarBurstAnim:Play() end
            if testFrame.LineSheenAnim then testFrame.LineSheenAnim:Play() end
            print("|cff00ff00ObjectiveTrackerHeader|r: 所有动画已播放")
        else
            print("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
        end

    elseif cmd == "stop" then
        -- 停止动画
        if testFrame then
            -- 停止所有动画组
            if testFrame.BackgroundAnim then testFrame.BackgroundAnim:Stop() end
            if testFrame.LineGlowAnim then testFrame.LineGlowAnim:Stop() end
            if testFrame.SoftGlowAnim then testFrame.SoftGlowAnim:Stop() end
            if testFrame.StarBurstAnim then testFrame.StarBurstAnim:Stop() end
            if testFrame.LineSheenAnim then testFrame.LineSheenAnim:Stop() end
            print("|cff00ff00ObjectiveTrackerHeader|r: 所有动画已停止")
        else
            print("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
        end

    elseif cmd == "reset" then
        -- 重置所有元素的透明度
        if testFrame then
            testFrame.Background:SetAlpha(1)
            testFrame.LineGlow:SetAlpha(0)
            testFrame.SoftGlow:SetAlpha(0)
            testFrame.StarBurst:SetAlpha(0)
            testFrame.LineSheen:SetAlpha(0)
            print("|cff00ff00ObjectiveTrackerHeader|r: 透明度已重置")
        else
            print("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
        end

    else
        -- 显示帮助信息
        print("|cff00ff00ObjectiveTrackerHeader 测试命令:|r")
        print("  /objheader show - 显示测试框架")
        print("  /objheader hide - 隐藏测试框架")
        print("  /objheader anim - 播放动画")
        print("  /objheader stop - 停止动画")
        print("  /objheader reset - 重置透明度")
    end
end
